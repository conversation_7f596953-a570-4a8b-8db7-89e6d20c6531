.social-icons {
  animation: slideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
}
.social-icons a {
  position: relative;
  margin-right: 20px;
  border: 2px solid var(--color-secondary);
  border-radius: 50%;

  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: var(--transition);
}

.social-icons a:hover {
  border: none;
  width: 100%;
  padding: 10px;
  aspect-ratio: 1;
  border-radius: 50%;

  -webkit-mask-composite: source-out;
  mask-composite: subtract;
  animation: spin 1s infinite linear;
}

.social-icons a::after {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  background-color: transparent;
  border-radius: 50%;
  border-bottom: 1px solid transparent;
  border-top: 1px solid transparent;
  transition: var(--transition);
  aspect-ratio: 1;
}

.social-icons a:hover::after {
  border-bottom-color: var(--color-primary);
  border-top-color: var(--color-primary);
  animation: rotate-in-center 1.2s linear infinite;
}

.social-icons a svg {
  transition: var(--transition);
  margin: 5px;
}

.social-icons a svg:hover {
  fill: var(--color-primary);
}

/* Enhanced Contact Section */

.form__container {
  position: relative;
  max-width: 600px;
  margin: 2rem 0 1rem auto;
  display: flex;
  flex-direction: column;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1),
    rgba(255, 255, 255, 0.05)
  );
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 1.5rem 1rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

.enhanced-form {
  animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

#contact .hero__img {
  width: 55%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Form Header */
.form__header {
  text-align: center;
  margin-bottom: 1.2rem;
  animation: fadeInDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.form__title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary);
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form__subtitle {
  font-size: 0.7rem;
  color: var(--color-secondary-text);
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

/* Enhanced Form */
.enhanced-contact-form {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.form__row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form__field {
  position: relative;
  margin-bottom: 0.5rem;
}

/* Input Wrapper */
.input-wrapper {
  position: relative;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  border: 2px solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.input-wrapper::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
  pointer-events: none;
  border-radius: 14px;
}

.input-wrapper.focused {
  border-color: var(--color-primary);
  background: rgba(255, 255, 255, 0.12);
  box-shadow: 0 0 0 4px rgba(84, 98, 255, 0.1), 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.input-wrapper.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.05);
  animation: shake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97);
}

/* Form Controls */
.form__control {
  width: 100%;
  padding: 1.25rem 1rem 0.75rem 1rem;
  background: transparent;
  border: none;
  outline: none;
  font-size: 1rem;
  color: var(--text-color);
  font-family: inherit;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form__control::placeholder {
  color: transparent;
}

.textarea-wrapper .form__control {
  resize: vertical;
  min-height: 120px;
  padding-top: 1.5rem;
  line-height: 1.5;
}

/* Floating Labels */
.floating-label {
  position: absolute;
  top: 50%;
  left: 1rem;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--color-secondary-text);
  font-size: 1rem;
  font-weight: 500;
  pointer-events: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2;
}

.textarea-wrapper .floating-label {
  top: 1.5rem;
  transform: none;
}

.floating-label.active,
.input-wrapper.focused .floating-label {
  top: -0.2rem;
  transform: translateY(0) scale(0.85);
  color: var(--color-primary);
  font-weight: 600;
}

.label-icon {
  font-size: 1rem;
  opacity: 0.8;
}

.label-text {
  white-space: nowrap;
}

/* Input Border Animation */
.input-border {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary), #7c3aed);
  transform: translateX(-50%);
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-wrapper.focused .input-border {
  width: 100%;
}

/* Character Counter */
.character-count {
  position: absolute;
  bottom: 0.5rem;
  right: 1rem;
  font-size: 0.75rem;
  color: var(--color-secondary-text);
  opacity: 0.7;
  transition: all 0.3s;
}

.character-count.over-limit {
  color: #ef4444;
  font-weight: 600;
}

/* Field Errors */
.field-error {
  color: #ef4444;
  font-size: 0.8rem;
  margin-top: 0.5rem;
  margin-left: 1rem;
  font-weight: 500;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.input-wrapper.error + .field-error {
  opacity: 1;
  transform: translateY(0);
}

/* Form Actions */
.form__actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

/* Enhanced Submit Button */
.btn__submit {
  position: relative;
  background: linear-gradient(135deg, var(--color-primary), #7c3aed);
  color: white;
  border: none;
  border-radius: 16px;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  min-width: 180px;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px rgba(84, 98, 255, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.btn__submit::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn__submit:hover::before {
  left: 100%;
}

.btn__submit:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(84, 98, 255, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

.btn__submit:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(84, 98, 255, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.btn__submit:disabled {
  cursor: not-allowed;
  opacity: 0.7;
  transform: none;
}

/* Button States */
.btn__submit.loading {
  pointer-events: none;
}

.btn__submit.loading .btn-text,
.btn__submit.loading .btn-icon {
  opacity: 0;
}

.btn__submit.loading .btn-loading {
  opacity: 1;
}

.btn__submit.success {
  background: linear-gradient(135deg, #10b981, #059669);
  animation: successPulse 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn__submit.error {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  animation: errorShake 0.5s cubic-bezier(0.36, 0.07, 0.19, 0.97);
}

/* Button Content */
.btn-text {
  transition: opacity 0.3s;
}

.btn-icon {
  font-size: 1.1rem;
  transition: all 0.3s;
}

.btn__submit.hovered .btn-icon {
  transform: translateX(4px);
}

/* Loading Spinner */
.btn-loading {
  position: absolute;
  opacity: 0;
  transition: opacity 0.3s;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Privacy Notice */
.form__privacy {
  text-align: center;
  opacity: 0.8;
}

.form__privacy small {
  color: var(--color-secondary-text);
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

/* Form Animations */
.enhanced-contact-form.shake {
  animation: formShake 0.6s cubic-bezier(0.36, 0.07, 0.19, 0.97);
}

.enhanced-contact-form.success-submitted {
  animation: successGlow 1s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Keyframe Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-2px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(2px);
  }
}

@keyframes formShake {
  0%,
  100% {
    transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translateX(-4px);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translateX(4px);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes successPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes errorShake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-4px);
  }
  75% {
    transform: translateX(4px);
  }
}

@keyframes successGlow {
  0% {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 20px 40px rgba(16, 185, 129, 0.3);
  }
  100% {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .form__container {
    max-width: 90%;
    margin: 1rem auto;
    padding: 1.5rem;
    border-radius: 20px;
  }

  #contact .hero__img {
    width: 100%;
  }

  .form__title {
    font-size: 1.75rem;
  }

  .form__subtitle {
    font-size: 0.9rem;
  }

  .form__row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .form__control {
    padding: 1rem 0.75rem 0.5rem 2.5rem;
    font-size: 0.9rem;
  }

  .floating-label {
    left: 0.75rem;
    font-size: 0.9rem;
  }

  .label-icon {
    font-size: 1rem;
  }

  .textarea-wrapper .form__control {
    min-height: 100px;
    padding-top: 1.25rem;
  }

  .btn__submit {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
    min-width: 160px;
  }

  .character-count {
    bottom: 0.25rem;
    right: 0.75rem;
    font-size: 0.7rem;
  }

  .field-error {
    margin-left: 0.75rem;
    font-size: 0.75rem;
  }

  .form__field .form__message {
    height: 300px;
  }

  .form__message + label {
    top: 97%;
  }

  .form__container {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .form__container {
    max-width: 95%;
    padding: 1rem;
    border-radius: 16px;
  }

  .form__header {
    margin-bottom: 1.5rem;
  }

  .form__title {
    font-size: 1.5rem;
  }

  .form__subtitle {
    font-size: 0.85rem;
  }

  .enhanced-contact-form {
    gap: 1.25rem;
  }

  .form__control {
    padding: 0.875rem 0.5rem 0.5rem 2.25rem;
    font-size: 0.85rem;
  }

  .floating-label {
    left: 0.5rem;
    font-size: 0.85rem;
    gap: 0.375rem;
  }

  .textarea-wrapper .form__control {
    min-height: 80px;
    padding-top: 1rem;
  }

  .btn__submit {
    padding: 0.75rem 1.25rem;
    font-size: 0.85rem;
    min-width: 140px;
  }

  .form__privacy small {
    font-size: 0.75rem;
  }

  .character-count {
    font-size: 0.65rem;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .form__container {
    border: 2px solid var(--color-primary);
    background: rgba(255, 255, 255, 0.15);
  }

  .input-wrapper {
    border: 2px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
  }

  .input-wrapper.focused {
    border-color: var(--color-primary);
    border-width: 3px;
  }

  .input-wrapper.error {
    border-color: #ef4444;
    border-width: 3px;
  }

  .btn__submit {
    border: 2px solid rgba(255, 255, 255, 0.3);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .form__container,
  .input-wrapper,
  .floating-label,
  .btn__submit,
  .field-error {
    transition: none;
    animation: none;
  }

  .enhanced-form {
    animation: none;
  }

  .form__header {
    animation: none;
  }

  .btn__submit::before {
    display: none;
  }

  .loading-spinner {
    animation: none;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .form__control {
    color: #ffffff;
  }

  .form__control::placeholder {
    color: rgba(255, 255, 255, 0.5);
  }

  .floating-label {
    color: rgba(255, 255, 255, 0.7);
  }

  .floating-label.active,
  .input-wrapper.focused .floating-label {
    color: var(--color-primary);
  }

  .character-count {
    color: rgba(255, 255, 255, 0.6);
  }

  .form__privacy small {
    color: rgba(255, 255, 255, 0.7);
  }
}
